# RuoYi框架 - 资产盘点菜单配置指南

## 📋 概述

本文档提供了在RuoYi框架中配置资产盘点管理模块菜单的完整指南，包括菜单结构、权限配置和操作步骤。

## 🏗️ 菜单结构

### 主菜单层级
```
资产盘点管理 (stocktaking)
├── 盘点计划 (plan)
├── 盘点任务 (task)  
├── 盘点记录 (record)
└── 盘点报告 (report)
```

### 详细菜单树
```
资产盘点管理
├── 盘点计划
│   ├── 计划查询 (stocktaking:plan:query)
│   ├── 计划新增 (stocktaking:plan:add)
│   ├── 计划修改 (stocktaking:plan:edit)
│   ├── 计划删除 (stocktaking:plan:remove)
│   ├── 计划导出 (stocktaking:plan:export)
│   └── 计划审批 (stocktaking:plan:approve)
├── 盘点任务
│   ├── 任务查询 (stocktaking:task:query)
│   ├── 任务新增 (stocktaking:task:add)
│   ├── 任务修改 (stocktaking:task:edit)
│   ├── 任务删除 (stocktaking:task:remove)
│   ├── 任务分配 (stocktaking:task:assign)
│   └── 任务执行 (stocktaking:task:execute)
├── 盘点记录
│   ├── 记录查询 (stocktaking:record:query)
│   ├── 记录新增 (stocktaking:record:add)
│   ├── 记录修改 (stocktaking:record:edit)
│   ├── 记录删除 (stocktaking:record:remove)
│   └── 记录导出 (stocktaking:record:export)
└── 盘点报告
    ├── 报告查询 (stocktaking:report:query)
    ├── 报告生成 (stocktaking:report:generate)
    ├── 报告导出 (stocktaking:report:export)
    └── 报告删除 (stocktaking:report:remove)
```

## 🔧 配置步骤

### 方式一：SQL脚本配置（推荐）

1. **执行SQL脚本**
   ```sql
   -- 执行 doc/资产盘点菜单配置.sql 文件
   ```

2. **注意事项**
   - 需要根据实际的menu_id调整parent_id
   - 确保菜单顺序符合业务逻辑
   - 检查权限标识符的唯一性

### 方式二：系统管理界面配置

1. **登录RuoYi管理后台**
   - 访问：http://localhost/login
   - 使用管理员账号登录

2. **进入菜单管理**
   - 系统管理 → 菜单管理

3. **添加主菜单**
   ```
   菜单名称：资产盘点管理
   父菜单：主类目
   显示排序：6
   路由地址：stocktaking
   菜单类型：目录
   菜单图标：clipboard
   ```

4. **添加子菜单**
   按照菜单结构逐一添加各级子菜单

## 📊 菜单配置详情

### 主要菜单配置

| 菜单名称 | 路由地址 | 组件路径 | 权限标识 | 图标 |
|---------|---------|----------|----------|------|
| 资产盘点管理 | stocktaking | - | - | clipboard |
| 盘点计划 | plan | assets/stocktaking/plan/index | stocktaking:plan:list | list |
| 盘点任务 | task | assets/stocktaking/task/index | stocktaking:task:list | skill |
| 盘点记录 | record | assets/stocktaking/record/index | stocktaking:record:list | documentation |
| 盘点报告 | report | assets/stocktaking/report/index | stocktaking:report:list | chart |

### 权限配置说明

#### 盘点计划权限
- `stocktaking:plan:query` - 查询计划
- `stocktaking:plan:add` - 新增计划
- `stocktaking:plan:edit` - 修改计划
- `stocktaking:plan:remove` - 删除计划
- `stocktaking:plan:export` - 导出计划
- `stocktaking:plan:approve` - 审批计划

#### 盘点任务权限
- `stocktaking:task:query` - 查询任务
- `stocktaking:task:add` - 新增任务
- `stocktaking:task:edit` - 修改任务
- `stocktaking:task:remove` - 删除任务
- `stocktaking:task:assign` - 分配任务
- `stocktaking:task:execute` - 执行任务

#### 盘点记录权限
- `stocktaking:record:query` - 查询记录
- `stocktaking:record:add` - 新增记录
- `stocktaking:record:edit` - 修改记录
- `stocktaking:record:remove` - 删除记录
- `stocktaking:record:export` - 导出记录

#### 盘点报告权限
- `stocktaking:report:query` - 查询报告
- `stocktaking:report:generate` - 生成报告
- `stocktaking:report:export` - 导出报告
- `stocktaking:report:remove` - 删除报告

## 🎯 角色权限分配建议

### 管理员角色
- 拥有所有权限
- 可以进行系统配置和用户管理

### 盘点主管角色
- 计划管理：查询、新增、修改、审批、导出
- 任务管理：查询、新增、修改、分配
- 记录管理：查询、修改、导出
- 报告管理：查询、生成、导出

### 盘点员角色
- 计划管理：查询
- 任务管理：查询、执行
- 记录管理：查询、新增、修改
- 报告管理：查询

### 查看者角色
- 所有模块：仅查询权限

## 🔍 验证配置

### 1. 检查菜单显示
- 登录系统后检查左侧菜单是否正确显示
- 验证菜单层级结构是否正确

### 2. 检查权限控制
- 使用不同角色账号测试权限控制
- 验证按钮权限是否生效

### 3. 检查路由跳转
- 点击菜单验证页面是否正确跳转
- 检查页面组件是否正确加载

## ⚠️ 注意事项

1. **菜单ID冲突**
   - 确保menu_id不与现有菜单冲突
   - 建议从2000开始分配ID

2. **权限标识符**
   - 权限标识符必须唯一
   - 遵循命名规范：模块:功能:操作

3. **组件路径**
   - 确保组件路径与实际文件路径一致
   - 注意大小写敏感

4. **图标选择**
   - 使用Element UI支持的图标
   - 保持图标风格一致

## 🚀 后续操作

1. **分配角色权限**
   - 系统管理 → 角色管理
   - 为不同角色分配相应权限

2. **用户授权**
   - 系统管理 → 用户管理
   - 为用户分配相应角色

3. **测试验证**
   - 使用不同权限用户测试功能
   - 验证权限控制是否正确

## 📝 常见问题

### Q: 菜单不显示怎么办？
A: 检查以下几点：
- 菜单状态是否为正常
- 用户是否有相应权限
- 组件路径是否正确

### Q: 权限控制不生效？
A: 检查以下几点：
- 权限标识符是否正确
- 角色是否分配了相应权限
- 用户是否分配了相应角色

### Q: 页面跳转404？
A: 检查以下几点：
- 路由配置是否正确
- 组件文件是否存在
- 路径大小写是否正确
