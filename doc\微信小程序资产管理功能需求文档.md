# 微信小程序资产管理功能需求文档

## 1. 项目概述

### 1.1 项目背景
基于现有的资产管理系统后端接口，开发微信小程序端，实现移动端资产管理功能，主要面向现场操作人员和管理人员使用。

### 1.2 技术架构
- **前端**: 微信小程序
- **后端**: RuoYi框架 + Spring Boot
- **数据库**: MySQL
- **认证**: JWT Token认证

### 1.3 用户角色
- **普通用户**: 资产查看、搜索
- **管理员**: 资产管理、系统管理

## 2. 核心功能模块

### 2.1 用户认证模块

#### 2.1.1 登录功能
- **页面**: `pages/login/login`
- **功能**:
  - 用户名密码登录
  - 记住登录状态
  - 自动登录
- **接口依赖**:
  - `POST /login` - 用户登录
  - `GET /getInfo` - 获取用户信息

#### 2.1.2 个人中心
- **页面**: `pages/profile/profile`
- **功能**:
  - 个人信息展示
  - 修改密码
  - 退出登录
- **接口依赖**:
  - `GET /system/user/profile` - 获取个人信息
  - `PUT /system/user/profile` - 修改个人信息

### 2.2 资产台账模块

#### 2.2.1 资产列表
- **页面**: `pages/asset/list/list`
- **功能**:
  - 资产列表展示（分页加载）
  - 搜索筛选（资产名称、编号、状态等）
  - 资产状态标识
  - 下拉刷新、上拉加载更多
- **接口依赖**:
  - `POST /asset/ledger/list` - 资产列表查询

#### 2.2.2 资产详情
- **页面**: `pages/asset/detail/detail`
- **功能**:
  - 资产基本信息展示
  - 资产图片查看
  - 维护记录查看
  - 关联备件查看
  - 操作记录查看
- **接口依赖**:
  - `GET /asset/ledger/detailWithMaintenance` - 资产详情查询
  - `GET /asset/part/relation/parts/{assetId}` - 关联备件查询

#### 2.2.3 资产搜索
- **页面**: `pages/asset/search/search`
- **功能**:
  - 扫码搜索资产
  - 关键词搜索
  - 高级筛选
  - 搜索历史
- **接口依赖**:
  - `POST /asset/ledger/list` - 资产搜索

## 3. 页面结构设计

### 3.1 底部导航栏
```
- 首页 (资产列表)
- 搜索 (资产搜索)
- 我的 (个人中心)
```

### 3.2 页面层级结构
```
pages/
├── index/                    # 首页(资产列表)
│   └── index
├── asset/                    # 资产模块
│   ├── detail/               # 资产详情
│   └── search/               # 资产搜索
├── profile/                  # 个人中心
│   └── profile
└── login/                    # 登录
    └── login
```

## 4. 技术要求

### 4.1 开发规范
- 使用微信小程序原生开发
- 遵循微信小程序设计规范
- 支持暗黑模式适配
- 响应式布局设计

### 4.2 性能要求
- 页面加载时间 < 2秒
- 支持离线缓存关键数据
- 图片懒加载
- 分页加载优化

### 4.3 兼容性要求
- 支持微信版本 7.0+
- 适配不同屏幕尺寸
- iOS/Android 双平台兼容

## 5. 接口对接说明

### 5.1 请求配置
- **Base URL**: 根据环境配置
- **认证方式**: Bearer Token (JWT)
- **请求头**: 
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```

### 5.2 错误处理
- 统一错误码处理
- 网络异常处理
- Token过期自动刷新
- 用户友好的错误提示

### 5.3 数据缓存
- 用户信息缓存
- 常用数据本地存储
- 离线数据同步机制

## 6. UI/UX设计要求

### 6.1 设计风格
- 简洁现代的设计风格
- 符合微信设计语言
- 突出功能性和易用性
- 适当的动画效果

### 6.2 交互设计
- 直观的操作流程
- 清晰的状态反馈
- 便捷的快捷操作
- 友好的错误提示

### 6.3 特殊功能
- 扫码功能集成
- 下拉刷新/上拉加载
- 搜索联想功能
- 资产状态标识

## 7. 开发优先级

### 7.1 第一期（核心功能）
1. 用户认证模块
2. 资产列表查看
3. 资产详情查看
4. 个人中心

### 7.2 第二期（扩展功能）
1. 资产搜索功能
2. 扫码搜索
3. 高级筛选
4. 搜索历史

### 7.3 第三期（优化功能）
1. 离线功能
2. 性能优化
3. 用户体验优化
4. 数据缓存优化

## 8. 测试要求

### 8.1 功能测试
- 各模块功能完整性测试
- 接口对接测试
- 异常情况处理测试

### 8.2 兼容性测试
- 不同设备型号测试
- 不同微信版本测试
- 网络环境测试

### 8.3 性能测试
- 页面加载性能测试
- 内存使用测试
- 电池消耗测试

## 9. 部署说明

### 9.1 小程序配置
- AppID配置
- 域名白名单配置
- 权限申请配置

### 9.2 版本管理
- 开发版本管理
- 体验版本发布
- 正式版本发布流程

## 10. 详细接口映射表

### 10.1 资产台账相关接口
| 功能 | 方法 | 接口路径 | 参数说明 | 返回数据 |
|------|------|----------|----------|----------|
| 资产列表查询 | POST | `/asset/ledger/list` | AssetSearchRequest (分页、筛选条件) | IPage<AssetBaseInfoVo> |
| 资产详情查询 | GET | `/asset/ledger/detail` | assetId | AssetDetailVo |
| 资产详情(含维护) | GET | `/asset/ledger/detailWithMaintenance` | assetId | AssetDetailVo (含维护信息) |
| 关联备件查询 | GET | `/asset/part/relation/parts/{assetId}` | assetId | List<AssetPartRelationVo> |

## 11. 数据模型说明

### 11.1 主要VO对象结构
```javascript
// 资产基本信息
AssetBaseInfoVo {
  assetId: string,           // 资产ID
  assetName: string,         // 资产名称
  assetCode: string,         // 资产编码
  assetType: string,         // 资产类型
  status: number,            // 资产状态
  location: string,          // 存放位置
  deptName: string,          // 所属部门
  purchasePrice: number,     // 采购价格
  purchaseDate: string,      // 采购日期
  supplier: string,          // 供应商
  model: string,             // 型号规格
  // ... 其他字段
}

// 资产详细信息
AssetDetailVo {
  // 包含AssetBaseInfoVo的所有字段
  description: string,       // 资产描述
  maintenancePlans: [],      // 维护计划列表
  maintenanceTasks: [],      // 维护任务列表
  images: [],                // 资产图片
  // ... 其他详细信息
}

// 资产备件关联信息
AssetPartRelationVo {
  relationId: string,        // 关联ID
  assetId: string,          // 资产ID
  partId: string,           // 备件ID
  partName: string,         // 备件名称
  partCode: string,         // 备件编码
  quantity: number,         // 数量
  // ... 其他字段
}
```

### 11.2 状态码说明
```javascript
// 资产状态
ASSET_STATUS = {
  NORMAL: 1,      // 正常
  REPAIR: 2,      // 维修中
  SCRAP: 3,       // 报废
  IDLE: 4         // 闲置
}
```

## 12. 小程序特殊功能实现

### 12.1 扫码搜索功能
```javascript
// 调用微信扫码API
wx.scanCode({
  success: (res) => {
    // 处理扫码结果
    const scanContent = res.result;
    // 根据扫码结果搜索资产
    this.searchAssetByCode(scanContent);
  }
});
```

### 12.2 分页加载实现
```javascript
// 分页加载资产列表
loadAssetList(pageNum = 1, pageSize = 20) {
  const searchRequest = {
    pageNum: pageNum,
    pageSize: pageSize,
    // 其他搜索条件
  };

  wx.request({
    url: baseUrl + '/asset/ledger/list',
    method: 'POST',
    data: searchRequest,
    success: (res) => {
      if (pageNum === 1) {
        this.setData({ assetList: res.data.records });
      } else {
        this.setData({
          assetList: [...this.data.assetList, ...res.data.records]
        });
      }
    }
  });
}
```

### 12.3 离线数据处理
```javascript
// 本地存储关键数据
wx.setStorageSync('assetList', assetData);
wx.setStorageSync('searchHistory', searchHistory);

// 网络恢复时同步数据
wx.onNetworkStatusChange((res) => {
  if (res.isConnected) {
    this.refreshAssetData();
  }
});
```

## 13. 开发注意事项

### 13.1 权限处理
- 根据用户角色显示不同功能
- 接口调用前检查权限
- 优雅处理权限不足的情况

### 13.2 数据安全
- 敏感数据加密存储
- 接口调用使用HTTPS
- Token及时更新和清理

### 13.3 用户体验
- 加载状态提示
- 网络异常处理
- 操作结果反馈
- 数据缓存优化

### 13.4 性能优化
- 图片压缩和懒加载
- 分页数据加载
- 避免频繁接口调用
- 合理使用缓存

---

**注意**:
1. 本文档基于现有后端接口设计，开发过程中如发现接口不足或需要调整，请及时沟通确认。
2. 所有接口都需要在请求头中携带JWT Token进行身份验证。
3. 建议先实现核心功能模块，再逐步完善扩展功能。
4. 开发过程中注意遵循微信小程序开发规范和最佳实践。
