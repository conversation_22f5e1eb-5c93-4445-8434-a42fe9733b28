# 微信小程序资产管理功能需求文档

## 1. 项目概述

### 1.1 项目背景
基于现有的资产管理系统后端接口，开发微信小程序端，实现移动端资产管理功能，主要面向现场操作人员和管理人员使用。

### 1.2 技术架构
- **前端**: 微信小程序
- **后端**: RuoYi框架 + Spring Boot
- **数据库**: MySQL
- **认证**: JWT Token认证

### 1.3 用户角色
- **普通用户**: 资产查看、盘点操作、申请提交
- **管理员**: 审批、统计查看、系统管理
- **盘点员**: 专门负责资产盘点工作

## 2. 核心功能模块

### 2.1 用户认证模块

#### 2.1.1 登录功能
- **页面**: `pages/login/login`
- **功能**: 
  - 用户名密码登录
  - 记住登录状态
  - 自动登录
- **接口依赖**: 
  - `POST /login` - 用户登录
  - `GET /getInfo` - 获取用户信息

#### 2.1.2 个人中心
- **页面**: `pages/profile/profile`
- **功能**:
  - 个人信息展示
  - 修改密码
  - 退出登录
- **接口依赖**:
  - `GET /system/user/profile` - 获取个人信息
  - `PUT /system/user/profile` - 修改个人信息

### 2.2 资产台账模块

#### 2.2.1 资产列表
- **页面**: `pages/asset/list/list`
- **功能**:
  - 资产列表展示（分页加载）
  - 搜索筛选（资产名称、编号、状态等）
  - 资产状态标识
  - 下拉刷新、上拉加载更多
- **接口依赖**:
  - `POST /asset/ledger/list` - 资产列表查询

#### 2.2.2 资产详情
- **页面**: `pages/asset/detail/detail`
- **功能**:
  - 资产基本信息展示
  - 资产图片查看
  - 维护记录查看
  - 关联备件查看
  - 操作记录查看
- **接口依赖**:
  - `GET /asset/ledger/detailWithMaintenance` - 资产详情查询
  - `GET /asset/part/relation/parts/{assetId}` - 关联备件查询

#### 2.2.3 资产搜索
- **页面**: `pages/asset/search/search`
- **功能**:
  - 扫码搜索资产
  - 关键词搜索
  - 高级筛选
  - 搜索历史
- **接口依赖**:
  - `POST /asset/ledger/list` - 资产搜索

### 2.3 资产盘点模块

#### 2.3.1 我的盘点任务
- **页面**: `pages/stocktaking/task/my-tasks`
- **功能**:
  - 待执行任务列表
  - 进行中任务列表
  - 已完成任务列表
  - 任务状态标识
  - 任务领取功能
- **接口依赖**:
  - `POST /asset/stocktaking/task/list` (使用 `onlyMyTasks=true` 参数)
  - `GET /asset/stocktaking/task/user/pending` - 待执行任务
  - `GET /asset/stocktaking/task/user/inprogress` - 进行中任务
  - `POST /asset/stocktaking/task/{taskId}/claim` - 领取任务

#### 2.3.2 盘点任务详情
- **页面**: `pages/stocktaking/task/detail`
- **功能**:
  - 任务基本信息
  - 盘点进度展示
  - 开始/完成任务
  - 盘点记录列表
  - 未盘点资产列表
- **接口依赖**:
  - `GET /asset/stocktaking/task/{taskId}` - 任务详情
  - `GET /asset/stocktaking/task/{taskId}/progress` - 任务进度
  - `POST /asset/stocktaking/task/{taskId}/start` - 开始任务
  - `POST /asset/stocktaking/task/{taskId}/complete` - 完成任务
  - `GET /asset/stocktaking/record/missing/{taskId}` - 未盘点资产

#### 2.3.3 扫码盘点
- **页面**: `pages/stocktaking/scan/scan`
- **功能**:
  - 扫码识别资产
  - 资产信息确认
  - 盘点状态选择（正常/异常/缺失）
  - 异常情况备注
  - 拍照上传
  - 批量盘点模式
- **接口依赖**:
  - `POST /asset/stocktaking/record/scan` - 扫码盘点
  - `GET /asset/ledger/detail` - 资产详情确认
  - `GET /asset/stocktaking/record/code/{assetCode}` - 根据编码查询记录

#### 2.3.4 手动盘点
- **页面**: `pages/stocktaking/manual/manual`
- **功能**:
  - 手动输入资产信息
  - 盘点结果录入
  - 异常情况处理
  - 批量录入
- **接口依赖**:
  - `POST /asset/stocktaking/record/manual` - 手动录入盘点
  - `POST /asset/stocktaking/record` - 创建盘点记录

#### 2.3.5 盘点记录
- **页面**: `pages/stocktaking/record/list`
- **功能**:
  - 我的盘点记录
  - 记录状态筛选
  - 记录详情查看
  - 记录修改（限定条件下）
- **接口依赖**:
  - `POST /asset/stocktaking/record/list` (使用 `onlyMyRecords=true` 参数)
  - `GET /asset/stocktaking/record/{recordId}` - 记录详情
  - `PUT /asset/stocktaking/record` - 修改记录

### 2.4 资产申请模块

#### 2.4.1 入库申请
- **页面**: `pages/inbound/apply/apply`
- **功能**:
  - 入库申请创建
  - 资产信息录入
  - 附件上传
  - 申请提交
- **接口依赖**:
  - `POST /asset/inbound` - 创建入库申请
  - `PUT /asset/inbound/submit/{inboundId}` - 提交申请

#### 2.4.2 处置申请
- **页面**: `pages/disposal/apply/apply`
- **功能**:
  - 处置申请创建
  - 处置原因说明
  - 资产选择
  - 申请提交
- **接口依赖**:
  - `POST /asset/disposal/create` - 创建处置申请
  - `POST /asset/disposal/submit/{disposalId}` - 提交申请

#### 2.4.3 申请记录
- **页面**: `pages/apply/record/record`
- **功能**:
  - 我的申请记录
  - 申请状态查看
  - 申请详情查看
  - 申请撤销（限定条件下）
- **接口依赖**:
  - `POST /asset/inbound/list` - 入库申请列表
  - `POST /asset/disposal/list` - 处置申请列表
  - `GET /asset/inbound/{inboundId}` - 入库申请详情
  - `GET /asset/disposal/detail/{disposalId}` - 处置申请详情

### 2.5 审批管理模块

#### 2.5.1 待办审批
- **页面**: `pages/approval/pending/pending`
- **功能**:
  - 待审批事项列表
  - 审批类型分类
  - 紧急程度标识
  - 快速审批
- **接口依赖**:
  - `GET /asset/disposal/pending` - 待审批处置申请
  - 其他待审批接口（根据权限）

#### 2.5.2 审批详情
- **页面**: `pages/approval/detail/detail`
- **功能**:
  - 申请详情查看
  - 审批意见填写
  - 审批操作（通过/拒绝）
  - 审批历史查看
- **接口依赖**:
  - `POST /asset/disposal/approve` - 处置审批
  - `PUT /asset/inbound/audit/{inboundId}/{status}` - 入库审批

### 2.6 统计报表模块

#### 2.6.1 资产概览
- **页面**: `pages/statistics/overview/overview`
- **功能**:
  - 资产总数统计
  - 资产状态分布
  - 资产价值统计
  - 趋势图表展示
- **接口依赖**:
  - `GET /asset/workbench/overview` - 资产概览
  - `GET /asset/workbench/status-distribution` - 状态分布
  - `GET /asset/workbench/value-statistics` - 价值统计

#### 2.6.2 盘点统计
- **页面**: `pages/statistics/stocktaking/stocktaking`
- **功能**:
  - 盘点进度统计
  - 差异统计分析
  - 部门盘点情况
  - 图表可视化
- **接口依赖**:
  - `GET /asset/stocktaking/report/summary/{planId}` - 盘点汇总
  - `GET /asset/stocktaking/report/chart/{planId}` - 图表数据
  - `GET /asset/stocktaking/difference/statistics/{planId}` - 差异统计

## 3. 页面结构设计

### 3.1 底部导航栏
```
- 首页 (资产概览)
- 资产 (资产台账)
- 盘点 (盘点功能)
- 申请 (申请管理)
- 我的 (个人中心)
```

### 3.2 页面层级结构
```
pages/
├── index/                    # 首页
│   └── index
├── asset/                    # 资产模块
│   ├── list/
│   ├── detail/
│   └── search/
├── stocktaking/              # 盘点模块
│   ├── task/
│   ├── scan/
│   ├── manual/
│   └── record/
├── apply/                    # 申请模块
│   ├── inbound/
│   ├── disposal/
│   └── record/
├── approval/                 # 审批模块
│   ├── pending/
│   └── detail/
├── statistics/               # 统计模块
│   ├── overview/
│   └── stocktaking/
├── profile/                  # 个人中心
│   └── profile
└── login/                    # 登录
    └── login
```

## 4. 技术要求

### 4.1 开发规范
- 使用微信小程序原生开发
- 遵循微信小程序设计规范
- 支持暗黑模式适配
- 响应式布局设计

### 4.2 性能要求
- 页面加载时间 < 2秒
- 支持离线缓存关键数据
- 图片懒加载
- 分页加载优化

### 4.3 兼容性要求
- 支持微信版本 7.0+
- 适配不同屏幕尺寸
- iOS/Android 双平台兼容

## 5. 接口对接说明

### 5.1 请求配置
- **Base URL**: 根据环境配置
- **认证方式**: Bearer Token (JWT)
- **请求头**: 
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```

### 5.2 错误处理
- 统一错误码处理
- 网络异常处理
- Token过期自动刷新
- 用户友好的错误提示

### 5.3 数据缓存
- 用户信息缓存
- 常用数据本地存储
- 离线数据同步机制

## 6. UI/UX设计要求

### 6.1 设计风格
- 简洁现代的设计风格
- 符合微信设计语言
- 突出功能性和易用性
- 适当的动画效果

### 6.2 交互设计
- 直观的操作流程
- 清晰的状态反馈
- 便捷的快捷操作
- 友好的错误提示

### 6.3 特殊功能
- 扫码功能集成
- 拍照上传功能
- 下拉刷新/上拉加载
- 搜索联想功能

## 7. 开发优先级

### 7.1 第一期（核心功能）
1. 用户认证模块
2. 资产台账查看
3. 基础盘点功能
4. 个人中心

### 7.2 第二期（扩展功能）
1. 申请管理模块
2. 审批功能
3. 统计报表
4. 高级盘点功能

### 7.3 第三期（优化功能）
1. 离线功能
2. 性能优化
3. 用户体验优化
4. 高级统计分析

## 8. 测试要求

### 8.1 功能测试
- 各模块功能完整性测试
- 接口对接测试
- 异常情况处理测试

### 8.2 兼容性测试
- 不同设备型号测试
- 不同微信版本测试
- 网络环境测试

### 8.3 性能测试
- 页面加载性能测试
- 内存使用测试
- 电池消耗测试

## 9. 部署说明

### 9.1 小程序配置
- AppID配置
- 域名白名单配置
- 权限申请配置

### 9.2 版本管理
- 开发版本管理
- 体验版本发布
- 正式版本发布流程

---

**注意**: 本文档基于现有后端接口设计，开发过程中如发现接口不足或需要调整，请及时沟通确认。
