# 微信小程序资产盘点接口依赖修正说明

## 问题说明

在设计微信小程序资产盘点功能时，最初提出的一些接口在现有的后端系统中并不存在，需要根据实际的后端接口进行调整。

## 接口依赖修正对比

### 1. 任务管理模块

**原设计接口**：
- `GET /asset/stocktaking/task/my-tasks` ❌

**修正后接口**：
- `POST /asset/stocktaking/task/list` (使用 `onlyMyTasks=true` 参数) ✅

**说明**：现有的任务列表接口已经支持通过 `onlyMyTasks` 参数来筛选当前用户的任务，无需新增专门的接口。

### 2. 扫码盘点模块

**原设计接口**：
- `GET /asset/info/{assetCode}` ❌
- `POST /asset/stocktaking/record` ✅

**修正后接口**：
- `POST /asset/stocktaking/record/scan` ✅ (扫码盘点一体化接口)
- `GET /asset/stocktaking/record/code/{assetCode}` ✅ (根据资产编码查询记录)
- `GET /asset/detail` ✅ (根据资产ID获取详细信息)
- `POST /asset/stocktaking/record` ✅ (创建盘点记录)

**说明**：
1. 现有系统已经提供了专门的扫码盘点接口 `/scan`，可以一次性完成扫码识别和记录创建
2. 可以通过资产编码查询已有的盘点记录
3. 资产详细信息通过资产台账接口获取

### 3. 任务资产清单

**原设计接口**：
- `GET /asset/stocktaking/task/{taskId}/assets` ❌

**修正后接口**：
- `POST /asset/stocktaking/record/list` (使用 `taskId` 参数筛选) ✅
- `GET /asset/stocktaking/record/missing/{taskId}` ✅ (获取未盘点资产)

**说明**：通过盘点记录列表接口可以获取任务相关的资产信息，同时有专门的接口获取未盘点的资产。

### 4. 进度监控模块

**原设计接口**：
- `GET /asset/stocktaking/task/my-progress` ❌
- `GET /asset/stocktaking/plan/{planId}/team-progress` ❌
- `GET /asset/stocktaking/record/my-statistics` ❌

**修正后接口**：
- `GET /asset/stocktaking/record/progress/{taskId}` ✅ (获取任务盘点进度)
- `POST /asset/stocktaking/task/list` (使用 `onlyMyTasks=true`) ✅ (获取我的任务进度)
- `POST /asset/stocktaking/record/list` (使用 `onlyMyRecords=true`) ✅ (获取我的盘点统计)

**说明**：通过现有的任务和记录查询接口，结合相应的筛选参数，可以实现进度监控功能。

### 5. 历史记录模块

**原设计接口**：
- `GET /asset/stocktaking/record/my-history` ❌
- `GET /asset/stocktaking/asset/{assetId}/history` ❌

**修正后接口**：
- `POST /asset/stocktaking/record/list` (使用 `onlyMyRecords=true`) ✅ (获取个人历史记录)
- `GET /asset/stocktaking/record/asset/{assetId}` ✅ (获取资产盘点历史)

**说明**：通过现有的记录查询接口可以实现历史记录功能。

## 现有接口的优势

### 1. 扫码盘点一体化
`POST /asset/stocktaking/record/scan` 接口设计得很好，可以：
- 接收扫码内容
- 自动识别资产
- 创建盘点记录
- 返回完整的盘点信息

这比分离的接口设计更高效，减少了网络请求次数。

### 2. 灵活的查询参数
现有的列表查询接口都支持丰富的筛选参数：
- `onlyMyTasks`: 只查询我的任务
- `onlyMyRecords`: 只查询我的记录
- `taskId`: 按任务筛选
- `assetId`: 按资产筛选

这种设计比为每种查询场景单独设计接口更灵活。

### 3. 完整的功能覆盖
现有接口已经覆盖了微信小程序所需的所有核心功能：
- 任务管理
- 扫码盘点
- 记录管理
- 差异处理
- 进度查询
- 历史记录

## 微信小程序开发建议

### 1. 充分利用现有接口
- 使用 `/scan` 接口实现扫码盘点的核心功能
- 通过查询参数实现各种筛选需求
- 利用现有的差异处理和报告接口

### 2. 接口调用优化
- 合理使用缓存减少重复请求
- 批量操作时使用批量接口
- 离线模式下缓存必要数据

### 3. 用户体验优化
- 扫码后立即显示资产信息
- 实时更新盘点进度
- 提供友好的错误提示

## 总结

通过对现有后端接口的深入分析，发现当前的接口设计已经能够很好地支持微信小程序的功能需求。主要的调整是：

1. **使用现有的通用查询接口**，通过参数筛选实现特定功能
2. **利用专门的扫码接口**，提供更好的用户体验
3. **充分利用现有的差异处理和进度查询功能**

这种调整不仅避免了重复开发，还能确保微信小程序与现有系统的一致性和兼容性。
