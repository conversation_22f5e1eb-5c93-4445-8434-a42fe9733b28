-- 资产盘点管理菜单配置SQL脚本
-- 适用于RuoYi框架

-- 1. 主菜单：资产盘点管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('资产盘点管理', 0, 6, 'stocktaking', NULL, 1, 0, 'M', '0', '0', NULL, 'clipboard', 'admin', sysdate(), '', NULL, '资产盘点管理目录');

-- 获取刚插入的主菜单ID（假设为2000，实际使用时需要查询获取）
-- SELECT menu_id FROM sys_menu WHERE menu_name = '资产盘点管理' AND parent_id = 0;

-- 2. 盘点计划管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('盘点计划', 2000, 1, 'plan', 'assets/stocktaking/plan/index', 1, 0, 'C', '0', '0', 'stocktaking:plan:list', 'list', 'admin', sysdate(), '', NULL, '盘点计划菜单');

-- 3. 盘点任务管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('盘点任务', 2000, 2, 'task', 'assets/stocktaking/task/index', 1, 0, 'C', '0', '0', 'stocktaking:task:list', 'skill', 'admin', sysdate(), '', NULL, '盘点任务菜单');

-- 4. 盘点记录管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('盘点记录', 2000, 3, 'record', 'assets/stocktaking/record/index', 1, 0, 'C', '0', '0', 'stocktaking:record:list', 'documentation', 'admin', sysdate(), '', NULL, '盘点记录菜单');

-- 5. 盘点报告管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('盘点报告', 2000, 4, 'report', 'assets/stocktaking/report/index', 1, 0, 'C', '0', '0', 'stocktaking:report:list', 'chart', 'admin', sysdate(), '', NULL, '盘点报告菜单');

-- 获取子菜单ID（实际使用时需要查询获取）
-- 盘点计划菜单ID：2001
-- 盘点任务菜单ID：2002  
-- 盘点记录菜单ID：2003
-- 盘点报告菜单ID：2004

-- 6. 盘点计划子菜单
-- 6.1 计划查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('计划查询', 2001, 1, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:plan:query', '#', 'admin', sysdate(), '', NULL, '');

-- 6.2 计划新增
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('计划新增', 2001, 2, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:plan:add', '#', 'admin', sysdate(), '', NULL, '');

-- 6.3 计划修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('计划修改', 2001, 3, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:plan:edit', '#', 'admin', sysdate(), '', NULL, '');

-- 6.4 计划删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('计划删除', 2001, 4, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:plan:remove', '#', 'admin', sysdate(), '', NULL, '');

-- 6.5 计划导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('计划导出', 2001, 5, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:plan:export', '#', 'admin', sysdate(), '', NULL, '');

-- 6.6 计划审批
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('计划审批', 2001, 6, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:plan:approve', '#', 'admin', sysdate(), '', NULL, '');

-- 7. 盘点任务子菜单
-- 7.1 任务查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('任务查询', 2002, 1, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:task:query', '#', 'admin', sysdate(), '', NULL, '');

-- 7.2 任务新增
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('任务新增', 2002, 2, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:task:add', '#', 'admin', sysdate(), '', NULL, '');

-- 7.3 任务修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('任务修改', 2002, 3, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:task:edit', '#', 'admin', sysdate(), '', NULL, '');

-- 7.4 任务删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('任务删除', 2002, 4, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:task:remove', '#', 'admin', sysdate(), '', NULL, '');

-- 7.5 任务分配
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('任务分配', 2002, 5, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:task:assign', '#', 'admin', sysdate(), '', NULL, '');

-- 7.6 任务执行
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('任务执行', 2002, 6, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:task:execute', '#', 'admin', sysdate(), '', NULL, '');

-- 8. 盘点记录子菜单
-- 8.1 记录查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('记录查询', 2003, 1, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:record:query', '#', 'admin', sysdate(), '', NULL, '');

-- 8.2 记录新增
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('记录新增', 2003, 2, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:record:add', '#', 'admin', sysdate(), '', NULL, '');

-- 8.3 记录修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('记录修改', 2003, 3, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:record:edit', '#', 'admin', sysdate(), '', NULL, '');

-- 8.4 记录删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('记录删除', 2003, 4, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:record:remove', '#', 'admin', sysdate(), '', NULL, '');

-- 8.5 记录导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('记录导出', 2003, 5, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:record:export', '#', 'admin', sysdate(), '', NULL, '');

-- 9. 盘点报告子菜单
-- 9.1 报告查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('报告查询', 2004, 1, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:report:query', '#', 'admin', sysdate(), '', NULL, '');

-- 9.2 报告生成
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('报告生成', 2004, 2, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:report:generate', '#', 'admin', sysdate(), '', NULL, '');

-- 9.3 报告导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('报告导出', 2004, 3, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:report:export', '#', 'admin', sysdate(), '', NULL, '');

-- 9.4 报告删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('报告删除', 2004, 4, '#', '', 1, 0, 'F', '0', '0', 'stocktaking:report:remove', '#', 'admin', sysdate(), '', NULL, '');

-- 提交事务
COMMIT;
