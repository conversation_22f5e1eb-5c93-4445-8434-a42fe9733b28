# 资产盘点菜单配置 - 快速操作指南

## 🚀 快速配置步骤

### 第一步：执行SQL脚本

1. **连接数据库**
   ```bash
   # 使用MySQL客户端连接数据库
   mysql -u root -p your_database_name
   ```

2. **执行菜单配置脚本**
   ```sql
   source doc/资产盘点菜单配置.sql;
   ```

3. **验证菜单插入**
   ```sql
   -- 查看插入的菜单
   SELECT menu_id, menu_name, parent_id, path, perms 
   FROM sys_menu 
   WHERE menu_name LIKE '%盘点%' 
   ORDER BY menu_id;
   ```

### 第二步：调整菜单ID（如有冲突）

如果菜单ID与现有菜单冲突，需要调整：

```sql
-- 查询当前最大菜单ID
SELECT MAX(menu_id) FROM sys_menu;

-- 如果需要调整，修改脚本中的ID值
-- 例如：将2000改为实际可用的ID
```

### 第三步：配置角色权限

1. **登录RuoYi管理后台**
   - 地址：http://localhost:8080
   - 账号：admin / admin123

2. **进入角色管理**
   - 系统管理 → 角色管理

3. **编辑角色权限**
   - 选择要配置的角色
   - 点击"修改"按钮
   - 在权限配置中勾选资产盘点相关权限

### 第四步：验证配置

1. **检查菜单显示**
   - 刷新页面
   - 查看左侧菜单是否显示"资产盘点管理"

2. **测试页面跳转**
   - 点击各级菜单
   - 验证页面是否正确加载

## 📋 权限分配建议

### 超级管理员
```
✅ 所有资产盘点权限
```

### 资产管理员
```
✅ stocktaking:plan:query
✅ stocktaking:plan:add
✅ stocktaking:plan:edit
✅ stocktaking:plan:approve
✅ stocktaking:plan:export
✅ stocktaking:task:query
✅ stocktaking:task:add
✅ stocktaking:task:edit
✅ stocktaking:task:assign
✅ stocktaking:record:query
✅ stocktaking:record:edit
✅ stocktaking:record:export
✅ stocktaking:report:query
✅ stocktaking:report:generate
✅ stocktaking:report:export
```

### 盘点员
```
✅ stocktaking:plan:query
✅ stocktaking:task:query
✅ stocktaking:task:execute
✅ stocktaking:record:query
✅ stocktaking:record:add
✅ stocktaking:record:edit
✅ stocktaking:report:query
```

### 查看者
```
✅ stocktaking:plan:query
✅ stocktaking:task:query
✅ stocktaking:record:query
✅ stocktaking:report:query
```

## 🔧 常用SQL命令

### 查询菜单信息
```sql
-- 查看所有盘点相关菜单
SELECT 
    menu_id,
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    perms,
    icon
FROM sys_menu 
WHERE menu_name LIKE '%盘点%' 
ORDER BY parent_id, order_num;
```

### 查询角色权限
```sql
-- 查看角色拥有的盘点权限
SELECT 
    r.role_name,
    m.menu_name,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_name LIKE '%盘点%'
ORDER BY r.role_name, m.menu_name;
```

### 删除菜单（如需重新配置）
```sql
-- 删除所有盘点相关菜单（谨慎操作）
DELETE FROM sys_menu WHERE menu_name LIKE '%盘点%';
```

## ⚠️ 故障排除

### 问题1：菜单不显示
**可能原因：**
- 用户没有相应权限
- 菜单状态为停用
- 缓存问题

**解决方案：**
```sql
-- 检查菜单状态
SELECT menu_name, status FROM sys_menu WHERE menu_name LIKE '%盘点%';

-- 启用菜单
UPDATE sys_menu SET status = '0' WHERE menu_name LIKE '%盘点%';
```

### 问题2：页面404错误
**可能原因：**
- 组件路径错误
- 前端文件不存在

**解决方案：**
1. 检查组件路径是否正确
2. 确认前端文件是否存在
3. 检查路由配置

### 问题3：权限控制不生效
**可能原因：**
- 权限标识符错误
- 角色权限未分配

**解决方案：**
```sql
-- 检查权限标识符
SELECT menu_name, perms FROM sys_menu WHERE menu_name LIKE '%盘点%';

-- 为角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE menu_name LIKE '%盘点%';
```

## 📞 技术支持

如果在配置过程中遇到问题，请检查：

1. **数据库连接**是否正常
2. **RuoYi版本**是否兼容
3. **前端文件**是否完整
4. **权限配置**是否正确

## 🎯 配置完成检查清单

- [ ] SQL脚本执行成功
- [ ] 菜单在后台显示正常
- [ ] 角色权限配置完成
- [ ] 用户权限分配完成
- [ ] 页面跳转正常
- [ ] 权限控制生效
- [ ] 功能测试通过

完成以上检查后，资产盘点菜单配置就完成了！
