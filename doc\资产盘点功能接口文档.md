# 资产盘点功能接口文档

## 概述

本文档描述了资产盘点功能的后端接口，包括盘点计划、盘点任务、盘点记录、差异分析和报告生成等模块的所有API接口。

## 基础信息

- **基础URL**: `/asset/stocktaking`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

### 分页响应
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [],
  "total": 0
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

## 1. 盘点计划管理接口

### 1.1 查询盘点计划列表

**接口地址**: `POST /asset/stocktaking/plan/list`

**请求参数**:
```json
{
  "planName": "计划名称（模糊查询）",
  "planType": 1,
  "responsibleUserId": 1,
  "status": 1,
  "statusList": [1, 2, 3],
  "startDateBegin": "2025-01-01",
  "startDateEnd": "2025-12-31",
  "endDateBegin": "2025-01-01",
  "endDateEnd": "2025-12-31",
  "createTimeBegin": "2025-01-01 00:00:00",
  "createTimeEnd": "2025-12-31 23:59:59",
  "deptId": 100,
  "includeSubDept": true,
  "orderBy": "createTime",
  "orderDirection": "desc",
  "pageNum": 1,
  "pageSize": 10,
  "includeStatistics": true,
  "keyword": "关键词搜索"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "planId": "1745678901234567890",
      "planName": "2025年第一季度资产盘点",
      "planType": 1,
      "planTypeDesc": "全盘",
      "planScope": "{\"deptIds\":[100,101],\"categoryIds\":[1,2]}",
      "startDate": "2025-01-15",
      "endDate": "2025-01-30",
      "responsibleUserId": 1,
      "responsibleUserName": "管理员",
      "status": 3,
      "statusDesc": "执行中",
      "createBy": "admin",
      "createTime": "2025-01-14 10:00:00",
      "updateBy": "admin",
      "updateTime": "2025-01-14 10:00:00",
      "remark": "第一季度例行盘点",
      "progressInfo": {
        "totalTasks": 10,
        "completedTasks": 3,
        "totalAssets": 500,
        "inventoriedAssets": 150,
        "completionRate": 30.0
      },
      "statisticsInfo": {
        "totalRecords": 150,
        "normalRecords": 140,
        "abnormalRecords": 10,
        "differenceRecords": 5
      }
    }
  ],
  "total": 1
}
```

### 1.2 获取盘点计划详情

**接口地址**: `GET /asset/stocktaking/plan/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "planId": "1745678901234567890",
    "planName": "2025年第一季度资产盘点",
    "planType": 1,
    "planTypeDesc": "全盘",
    "planScope": "{\"deptIds\":[100,101],\"categoryIds\":[1,2]}",
    "startDate": "2025-01-15",
    "endDate": "2025-01-30",
    "responsibleUserId": 1,
    "responsibleUserName": "管理员",
    "status": 3,
    "statusDesc": "执行中",
    "createBy": "admin",
    "createTime": "2025-01-14 10:00:00",
    "remark": "第一季度例行盘点"
  }
}
```

### 1.3 新增盘点计划

**接口地址**: `POST /asset/stocktaking/plan`

**请求参数**:
```json
{
  "planName": "2025年第一季度资产盘点",
  "planType": 1,
  "planScope": "{\"deptIds\":[100,101],\"categoryIds\":[1,2]}",
  "startDate": "2025-01-15",
  "endDate": "2025-01-30",
  "responsibleUserId": 1,
  "remark": "第一季度例行盘点",
  "scopeDetail": {
    "deptIds": [100, 101],
    "categoryIds": [1, 2],
    "locationIds": [1, 2, 3],
    "statusList": [1, 2],
    "assetIds": [],
    "includeSubDept": true,
    "minValue": 1000.0,
    "maxValue": 100000.0
  }
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "新增盘点计划成功",
  "data": "1745678901234567890"
}
```

### 1.4 修改盘点计划

**接口地址**: `PUT /asset/stocktaking/plan`

**请求参数**: 同新增接口，需包含 `planId` 字段

**响应数据**:
```json
{
  "code": 200,
  "msg": "修改盘点计划成功"
}
```

### 1.5 删除盘点计划

**接口地址**: `DELETE /asset/stocktaking/plan/{planIds}`

**路径参数**:
- `planIds`: 盘点计划ID，多个用逗号分隔

**响应数据**:
```json
{
  "code": 200,
  "msg": "删除盘点计划成功"
}
```

### 1.6 导出盘点计划

**接口地址**: `POST /asset/stocktaking/plan/export`

**请求参数**: 同查询列表接口

**响应**: Excel文件下载

### 1.7 启动盘点计划

**接口地址**: `PUT /asset/stocktaking/plan/start/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "启动盘点计划成功"
}
```

### 1.8 完成盘点计划

**接口地址**: `PUT /asset/stocktaking/plan/complete/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "完成盘点计划成功"
}
```

### 1.9 取消盘点计划

**接口地址**: `PUT /asset/stocktaking/plan/cancel/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**请求参数**:
```json
{
  "cancelReason": "取消原因"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "取消盘点计划成功"
}
```

### 1.10 生成盘点任务

**接口地址**: `POST /asset/stocktaking/plan/generate-tasks/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**请求参数**:
```json
{
  "distributionType": 1,
  "maxAssetCount": 100,
  "assignedUserIds": [1, 2, 3],
  "autoAssign": true,
  "priority": 2
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "生成盘点任务成功",
  "data": {
    "generatedTaskCount": 5,
    "taskIds": ["task1", "task2", "task3", "task4", "task5"]
  }
}
```

## 2. 盘点任务管理接口

### 2.1 查询盘点任务列表

**接口地址**: `POST /asset/stocktaking/task/list`

**请求参数**:
```json
{
  "planId": "1745678901234567890",
  "taskName": "任务名称（模糊查询）",
  "assignedUserId": 1,
  "status": 1,
  "statusList": [1, 2, 3],
  "startTimeBegin": "2025-01-15 00:00:00",
  "startTimeEnd": "2025-01-30 23:59:59",
  "endTimeBegin": "2025-01-15 00:00:00",
  "endTimeEnd": "2025-01-30 23:59:59",
  "expectedCountMin": 10,
  "expectedCountMax": 100,
  "progressMin": 0.0,
  "progressMax": 100.0,
  "onlyMyTasks": false,
  "onlyOverdueTasks": false,
  "deptId": 100,
  "orderBy": "createTime",
  "orderDirection": "desc",
  "pageNum": 1,
  "pageSize": 10,
  "includeProgress": true,
  "includeStatistics": true,
  "keyword": "关键词搜索"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "taskId": "1745678901234567891",
      "planId": "1745678901234567890",
      "planName": "2025年第一季度资产盘点",
      "taskName": "办公区域盘点任务",
      "assignedUserId": 1,
      "assignedUserName": "张三",
      "assetScope": "{\"deptIds\":[100],\"locationIds\":[1,2]}",
      "expectedCount": 50,
      "actualCount": 30,
      "status": 2,
      "statusDesc": "执行中",
      "startTime": "2025-01-15 09:00:00",
      "endTime": null,
      "createBy": "admin",
      "createTime": "2025-01-14 10:00:00",
      "progressInfo": {
        "completionRate": 60.0,
        "remainingCount": 20,
        "estimatedEndTime": "2025-01-16 15:00:00"
      },
      "statistics": {
        "totalRecords": 30,
        "normalRecords": 28,
        "abnormalRecords": 2,
        "differenceRecords": 1,
        "lastInventoryTime": "2025-01-15 14:30:00"
      }
    }
  ],
  "total": 1
}
```

### 2.2 获取盘点任务详情

**接口地址**: `GET /asset/stocktaking/task/{taskId}`

**路径参数**:
- `taskId`: 盘点任务ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "1745678901234567891",
    "planId": "1745678901234567890",
    "planName": "2025年第一季度资产盘点",
    "taskName": "办公区域盘点任务",
    "assignedUserId": 1,
    "assignedUserName": "张三",
    "assetScope": "{\"deptIds\":[100],\"locationIds\":[1,2]}",
    "expectedCount": 50,
    "actualCount": 30,
    "status": 2,
    "statusDesc": "执行中",
    "startTime": "2025-01-15 09:00:00",
    "endTime": null,
    "createBy": "admin",
    "createTime": "2025-01-14 10:00:00"
  }
}
```

### 2.3 新增盘点任务

**接口地址**: `POST /asset/stocktaking/task`

**请求参数**:
```json
{
  "planId": "1745678901234567890",
  "taskName": "办公区域盘点任务",
  "assignedUserId": 1,
  "assetScope": "{\"deptIds\":[100],\"locationIds\":[1,2]}",
  "expectedCount": 50,
  "distributionConfig": {
    "distributionType": 1,
    "maxAssetCount": 100,
    "assignedUserIds": [1],
    "autoAssign": true,
    "priority": 2
  }
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "新增盘点任务成功",
  "data": "1745678901234567891"
}
```

### 2.4 修改盘点任务

**接口地址**: `PUT /asset/stocktaking/task`

**请求参数**: 同新增接口，需包含 `taskId` 字段

**响应数据**:
```json
{
  "code": 200,
  "msg": "修改盘点任务成功"
}
```

### 2.5 删除盘点任务

**接口地址**: `DELETE /asset/stocktaking/task/{taskIds}`

**路径参数**:
- `taskIds`: 盘点任务ID，多个用逗号分隔

**响应数据**:
```json
{
  "code": 200,
  "msg": "删除盘点任务成功"
}
```

### 2.6 导出盘点任务

**接口地址**: `POST /asset/stocktaking/task/export`

**请求参数**: 同查询列表接口

**响应**: Excel文件下载

### 2.7 开始执行任务

**接口地址**: `PUT /asset/stocktaking/task/start/{taskId}`

**路径参数**:
- `taskId`: 盘点任务ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "开始执行任务成功"
}
```

### 2.8 完成任务

**接口地址**: `PUT /asset/stocktaking/task/complete/{taskId}`

**路径参数**:
- `taskId`: 盘点任务ID

**请求参数**:
```json
{
  "completionRemark": "任务完成备注"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "完成任务成功"
}
```

### 2.9 重新分配任务

**接口地址**: `PUT /asset/stocktaking/task/reassign/{taskId}`

**路径参数**:
- `taskId`: 盘点任务ID

**请求参数**:
```json
{
  "newAssignedUserId": 2,
  "reassignReason": "重新分配原因"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "重新分配任务成功"
}
```

### 2.10 获取任务进度

**接口地址**: `GET /asset/stocktaking/task/progress/{taskId}`

**路径参数**:
- `taskId`: 盘点任务ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "1745678901234567891",
    "expectedCount": 50,
    "actualCount": 30,
    "completionRate": 60.0,
    "remainingCount": 20,
    "estimatedEndTime": "2025-01-16 15:00:00",
    "lastInventoryTime": "2025-01-15 14:30:00"
  }
}
```

## 3. 盘点记录管理接口

### 3.1 查询盘点记录列表

**接口地址**: `POST /asset/stocktaking/record/list`

**请求参数**:
```json
{
  "taskId": "1745678901234567891",
  "planId": "1745678901234567890",
  "assetId": "asset123",
  "assetCode": "A001",
  "assetName": "资产名称（模糊查询）",
  "foundStatus": 1,
  "actualStatus": 1,
  "inventoryUserId": 1,
  "inventoryTimeBegin": "2025-01-15 00:00:00",
  "inventoryTimeEnd": "2025-01-15 23:59:59",
  "categoryIds": [1, 2],
  "deptIds": [100, 101],
  "locationIds": [1, 2, 3],
  "onlyAbnormal": false,
  "onlyDifference": false,
  "onlyMyRecords": false,
  "assetValueMin": 1000.0,
  "assetValueMax": 100000.0,
  "deptId": 100,
  "orderBy": "inventoryTime",
  "orderDirection": "desc",
  "pageNum": 1,
  "pageSize": 10,
  "includeAssetInfo": true,
  "includeDifferenceInfo": true,
  "keyword": "关键词搜索"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "recordId": "1745678901234567892",
      "taskId": "1745678901234567891",
      "taskName": "办公区域盘点任务",
      "assetId": "asset123",
      "assetCode": "A001",
      "assetName": "办公桌",
      "categoryName": "办公家具",
      "foundStatus": 1,
      "foundStatusDesc": "找到",
      "actualLocation": "办公室101",
      "bookLocation": "办公室101",
      "actualStatus": 1,
      "actualStatusDesc": "正常",
      "bookStatus": 1,
      "bookStatusDesc": "正常",
      "inventoryUserId": 1,
      "inventoryUserName": "张三",
      "inventoryTime": "2025-01-15 14:30:00",
      "remark": "盘点正常",
      "createBy": "zhangsan",
      "createTime": "2025-01-15 14:30:00",
      "assetInfo": {
        "assetValue": 2000.0,
        "purchaseDate": "2024-01-01",
        "deptName": "行政部",
        "locationName": "办公室101"
      },
      "differenceInfo": {
        "hasDifference": false,
        "diffType": null,
        "diffTypeDesc": null,
        "diffReason": null,
        "handleStatus": null,
        "handleSuggestion": null
      }
    }
  ],
  "total": 1
}
```

### 3.2 获取盘点记录详情

**接口地址**: `GET /asset/stocktaking/record/{recordId}`

**路径参数**:
- `recordId`: 盘点记录ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "recordId": "1745678901234567892",
    "taskId": "1745678901234567891",
    "taskName": "办公区域盘点任务",
    "assetId": "asset123",
    "assetCode": "A001",
    "assetName": "办公桌",
    "foundStatus": 1,
    "foundStatusDesc": "找到",
    "actualLocation": "办公室101",
    "actualStatus": 1,
    "inventoryTime": "2025-01-15 14:30:00",
    "remark": "盘点正常"
  }
}
```

### 3.3 新增盘点记录

**接口地址**: `POST /asset/stocktaking/record`

**请求参数**:
```json
{
  "taskId": "1745678901234567891",
  "assetId": "asset123",
  "assetCode": "A001",
  "foundStatus": 1,
  "actualLocation": "办公室101",
  "actualStatus": 1,
  "inventoryTime": "2025-01-15 14:30:00",
  "remark": "盘点正常"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "新增盘点记录成功",
  "data": "1745678901234567892"
}
```

### 3.4 修改盘点记录

**接口地址**: `PUT /asset/stocktaking/record`

**请求参数**: 同新增接口，需包含 `recordId` 字段

**响应数据**:
```json
{
  "code": 200,
  "msg": "修改盘点记录成功"
}
```

### 3.5 删除盘点记录

**接口地址**: `DELETE /asset/stocktaking/record/{recordIds}`

**路径参数**:
- `recordIds`: 盘点记录ID，多个用逗号分隔

**响应数据**:
```json
{
  "code": 200,
  "msg": "删除盘点记录成功"
}
```

### 3.6 导出盘点记录

**接口地址**: `POST /asset/stocktaking/record/export`

**请求参数**: 同查询列表接口

**响应**: Excel文件下载

### 3.7 批量新增盘点记录

**接口地址**: `POST /asset/stocktaking/record/batch`

**请求参数**:
```json
{
  "taskId": "1745678901234567891",
  "batchRecords": [
    {
      "assetId": "asset123",
      "assetCode": "A001",
      "foundStatus": 1,
      "actualLocation": "办公室101",
      "actualStatus": 1,
      "inventoryTime": "2025-01-15 14:30:00",
      "remark": "盘点正常"
    }
  ]
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "批量新增盘点记录成功",
  "data": {
    "successCount": 10,
    "failCount": 0,
    "recordIds": ["id1", "id2", "id3"]
  }
}
```

### 3.8 根据资产ID查询记录

**接口地址**: `GET /asset/stocktaking/record/asset/{assetId}`

**路径参数**:
- `assetId`: 资产ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "recordId": "1745678901234567892",
      "taskId": "1745678901234567891",
      "assetId": "asset123",
      "foundStatus": 1,
      "inventoryTime": "2025-01-15 14:30:00"
    }
  ]
}
```

### 3.9 查询异常记录

**接口地址**: `GET /asset/stocktaking/record/abnormal/{taskId}`

**路径参数**:
- `taskId`: 盘点任务ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "recordId": "1745678901234567893",
      "assetId": "asset124",
      "assetCode": "A002",
      "foundStatus": 0,
      "remark": "资产未找到"
    }
  ]
}
```

## 4. 差异分析管理接口

### 4.1 查询盘点差异列表

**接口地址**: `GET /asset/stocktaking/difference/list`

**请求参数**:
- `planId`: 盘点计划ID（必填）
- `diffType`: 差异类型（可选）：1-盘盈，2-盘亏，3-状态差异，4-位置差异
- `handleStatus`: 处理状态（可选）：1-待处理，2-处理中，3-已处理
- `pageNum`: 页码（默认1）
- `pageSize`: 每页大小（默认10）

**响应数据**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "diffId": "1745678901234567893",
      "planId": "1745678901234567890",
      "assetId": "asset123",
      "assetCode": "A001",
      "assetName": "办公桌",
      "diffType": 4,
      "diffTypeDesc": "位置差异",
      "diffReason": "资产位置发生变更",
      "handleStatus": 1,
      "handleStatusDesc": "待处理",
      "handleSuggestion": "更新资产位置信息",
      "bookValue": "办公室101",
      "actualValue": "办公室102",
      "createTime": "2025-01-15 15:00:00"
    }
  ],
  "total": 1
}
```

### 4.2 获取盘点差异详情

**接口地址**: `GET /asset/stocktaking/difference/{diffId}`

**路径参数**:
- `diffId`: 差异ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "diffId": "1745678901234567893",
    "planId": "1745678901234567890",
    "assetId": "asset123",
    "diffType": 4,
    "diffReason": "资产位置发生变更",
    "handleStatus": 1,
    "handleSuggestion": "更新资产位置信息",
    "bookValue": "办公室101",
    "actualValue": "办公室102",
    "createTime": "2025-01-15 15:00:00"
  }
}
```

### 4.3 新增盘点差异

**接口地址**: `POST /asset/stocktaking/difference`

**请求参数**:
```json
{
  "planId": "1745678901234567890",
  "assetId": "asset123",
  "diffType": 4,
  "diffReason": "资产位置发生变更",
  "handleStatus": 1,
  "handleSuggestion": "更新资产位置信息",
  "bookValue": {
    "location": "办公室101",
    "status": 1,
    "value": 2000.0
  },
  "actualValue": {
    "location": "办公室102",
    "status": 1,
    "value": 2000.0
  }
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "创建盘点差异成功",
  "data": "1745678901234567893"
}
```

### 4.4 处理盘点差异

**接口地址**: `PUT /asset/stocktaking/difference/handle/{diffId}`

**路径参数**:
- `diffId`: 差异ID

**请求参数**:
```json
{
  "handleMethod": 1,
  "handleRemark": "已更新资产位置信息",
  "handleResult": "处理完成"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "处理盘点差异成功"
}
```

### 4.5 批量处理差异

**接口地址**: `PUT /asset/stocktaking/difference/batch-handle`

**请求参数**:
```json
{
  "diffIds": ["diff1", "diff2", "diff3"],
  "handleMethod": 1,
  "handleRemark": "批量处理差异"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "批量处理差异成功",
  "data": {
    "successCount": 3,
    "failCount": 0
  }
}
```

### 4.6 导出差异列表

**接口地址**: `POST /asset/stocktaking/difference/export`

**请求参数**: 同查询列表接口

**响应**: Excel文件下载

### 4.7 获取差异统计

**接口地址**: `GET /asset/stocktaking/difference/statistics/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalDifferences": 10,
    "surplusCount": 2,
    "deficitCount": 3,
    "statusDiffCount": 2,
    "locationDiffCount": 3,
    "surplusValue": 5000.0,
    "deficitValue": 8000.0,
    "differenceRate": 2.5,
    "processedDifferences": 6,
    "pendingDifferences": 4
  }
}
```

## 5. 报告生成管理接口

### 5.1 生成盘点汇总报告

**接口地址**: `GET /asset/stocktaking/report/summary/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "planId": "1745678901234567890",
    "planName": "2025年第一季度资产盘点",
    "reportTime": "2025-01-16 10:00:00",
    "summaryInfo": {
      "planStartDate": "2025-01-15",
      "planEndDate": "2025-01-30",
      "actualStartTime": "2025-01-15 09:00:00",
      "actualEndTime": "2025-01-16 18:00:00",
      "responsibleUserName": "管理员",
      "participantCount": 5,
      "totalTasks": 10,
      "completedTasks": 10,
      "totalAssets": 500,
      "inventoriedAssets": 495,
      "completionRate": 99.0,
      "totalAssetValue": 1000000.0,
      "inventoriedAssetValue": 995000.0
    },
    "differenceStatistics": {
      "totalDifferences": 10,
      "surplusCount": 2,
      "deficitCount": 3,
      "statusDiffCount": 2,
      "locationDiffCount": 3,
      "differenceRate": 2.0
    },
    "deptStatisticsList": [
      {
        "deptId": 100,
        "deptName": "行政部",
        "totalAssets": 100,
        "inventoriedAssets": 98,
        "completionRate": 98.0,
        "differenceCount": 2
      }
    ]
  }
}
```

### 5.2 生成部门统计报告

**接口地址**: `GET /asset/stocktaking/report/dept/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "deptId": 100,
      "deptName": "行政部",
      "totalAssets": 100,
      "inventoriedAssets": 98,
      "completionRate": 98.0,
      "differenceCount": 2,
      "surplusCount": 1,
      "deficitCount": 1,
      "totalValue": 200000.0,
      "differenceValue": 5000.0
    }
  ]
}
```

### 5.3 生成差异统计报告

**接口地址**: `GET /asset/stocktaking/report/difference/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalDifferences": 10,
    "surplusCount": 2,
    "deficitCount": 3,
    "statusDiffCount": 2,
    "locationDiffCount": 3,
    "surplusValue": 5000.0,
    "deficitValue": 8000.0,
    "differenceRate": 2.0,
    "processedDifferences": 6,
    "pendingDifferences": 4
  }
}
```

### 5.4 导出盘点报告

**接口地址**: `POST /asset/stocktaking/report/export/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**请求参数**:
```json
{
  "reportType": 1,
  "includeDetails": true,
  "includeDifferences": true,
  "includeStatistics": true
}
```

**响应**: Excel文件下载

### 5.5 生成图表数据

**接口地址**: `GET /asset/stocktaking/report/chart/{planId}`

**路径参数**:
- `planId`: 盘点计划ID

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "completionChart": {
      "labels": ["已完成", "进行中", "未开始"],
      "data": [80, 15, 5]
    },
    "differenceChart": {
      "labels": ["盘盈", "盘亏", "状态差异", "位置差异"],
      "data": [2, 3, 2, 3]
    },
    "deptChart": {
      "labels": ["行政部", "财务部", "技术部"],
      "data": [98, 95, 99]
    }
  }
}
```

## 6. 数据字典

### 6.1 盘点计划状态
- `1`: 草稿
- `2`: 待审批
- `3`: 执行中
- `4`: 已完成
- `5`: 已取消

### 6.2 盘点类型
- `1`: 全盘
- `2`: 部分盘点

### 6.3 任务状态
- `1`: 待执行
- `2`: 执行中
- `3`: 已完成

### 6.4 发现状态
- `0`: 未找到
- `1`: 找到

### 6.5 差异类型
- `1`: 盘盈
- `2`: 盘亏
- `3`: 状态差异
- `4`: 位置差异

### 6.6 处理状态
- `1`: 待处理
- `2`: 处理中
- `3`: 已处理

### 6.7 分发方式
- `1`: 按部门
- `2`: 按位置
- `3`: 按资产数量
- `4`: 手动指定

### 6.8 优先级
- `1`: 高
- `2`: 中
- `3`: 低

## 7. 错误码说明

### 7.1 通用错误码
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 7.2 业务错误码
- `10001`: 盘点计划不存在
- `10002`: 盘点计划状态不允许此操作
- `10003`: 盘点任务不存在
- `10004`: 盘点任务已完成，不允许修改
- `10005`: 盘点记录不存在
- `10006`: 资产不存在
- `10007`: 差异记录不存在
- `10008`: 权限不足，无法访问此资源

## 8. 注意事项

1. **权限控制**: 所有接口都需要相应的权限验证，具体权限码见接口注解
2. **数据权限**: 查询接口会根据用户的数据权限过滤结果
3. **分页查询**: 列表查询接口支持分页，默认每页10条记录
4. **时间格式**: 日期时间统一使用 `yyyy-MM-dd HH:mm:ss` 格式
5. **JSON格式**: 复杂对象字段（如范围配置）以JSON字符串形式存储
6. **文件导出**: 导出接口返回Excel文件，需要前端处理文件下载
7. **批量操作**: 支持批量新增、修改、删除操作，提高操作效率
8. **状态流转**: 计划和任务状态有严格的流转规则，不允许跳跃式变更
