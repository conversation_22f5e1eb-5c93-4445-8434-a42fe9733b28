# 微信小程序资产盘点功能设计文档

## 概述

基于已完成的资产盘点后端功能和网页端前端开发，设计微信小程序端资产盘点功能，为现场盘点人员提供便捷、高效的移动端盘点工具。

## 设计目标

- **移动化作业**：充分利用移动设备的扫码、拍照等特性
- **简化操作**：针对现场作业优化操作流程，减少操作步骤
- **离线支持**：支持网络不稳定环境下的离线盘点
- **实时同步**：与后端系统实时同步盘点数据

## 功能模块设计


### 2. 盘点任务管理模块

#### 2.1 我的盘点任务
- **功能描述**：查看分配给当前用户的盘点任务列表
- **显示信息**：
  - 任务名称和编号
  - 盘点计划名称
  - 任务状态（待执行、执行中、已完成）
  - 预期盘点数量和实际完成数量
  - 完成进度百分比
  - 任务截止时间
- **操作功能**：
  - 查看任务详情
  - 开始/继续盘点
  - 查看盘点进度
- **接口需求**：
  - `GET /asset/stocktaking/task/my-tasks` - 获取我的任务列表
  - `GET /asset/stocktaking/task/{taskId}` - 获取任务详情
  - `PUT /asset/stocktaking/task/start/{taskId}` - 开始执行任务

#### 2.2 任务详情查看
- **功能描述**：查看盘点任务的详细信息和资产清单
- **显示信息**：
  - 任务基本信息（名称、范围、截止时间等）
  - 待盘点资产列表
  - 已完成盘点的资产列表
  - 存在差异的资产列表
- **筛选功能**：
  - 按资产状态筛选（待盘点、已完成、有差异）
  - 按资产分类筛选
  - 按存放位置筛选
- **接口需求**：
  - `POST /asset/stocktaking/record/list` - 获取盘点记录列表（按taskId筛选）
  - `GET /asset/stocktaking/record/missing/{taskId}` - 获取未盘点资产列表

### 3. 资产盘点执行模块

#### 3.1 扫码盘点
- **功能描述**：通过扫描资产二维码或条形码快速定位资产
- **技术实现**：
  - 使用 `wx.scanCode` API 扫描资产标签
  - 支持二维码和条形码识别
  - 支持手动输入资产编码
- **操作流程**：
  ```
  扫码 → 识别资产 → 显示资产信息 → 确认位置状态 → 拍照记录 → 保存结果
  ```
- **接口需求**：
  - `POST /asset/stocktaking/record/scan` - 扫码盘点（包含资产信息获取和记录创建）
  - `GET /asset/detail` - 根据资产ID获取详细信息

#### 3.2 资产信息确认
- **功能描述**：确认扫描到的资产信息，录入实际状态
- **确认内容**：
  - 资产基本信息（名称、编码、规格等）
  - 账面位置 vs 实际位置
  - 账面状态 vs 实际状态
  - 资产外观状况
- **状态选项**：
  - 发现状态：找到/未找到
  - 资产状态：正常/损坏/报废/维修中
  - 位置状态：位置正确/位置变更
- **接口需求**：
  - `PUT /asset/stocktaking/record/{recordId}` - 更新盘点记录

#### 3.3 差异处理
- **功能描述**：处理盘点过程中发现的差异情况
- **差异类型**：
  - 盘盈：发现账面没有的资产
  - 盘亏：账面有但实际未找到的资产
  - 状态差异：资产状态与账面不符
  - 位置差异：资产位置与账面不符
- **处理操作**：
  - 选择差异类型和原因
  - 填写差异说明
  - 拍照记录差异情况
  - 提交差异报告
- **接口需求**：
  - `POST /asset/stocktaking/difference` - 创建差异记录
  - `PUT /asset/stocktaking/difference/{diffId}` - 更新差异处理

### 4. 现场记录模块

#### 4.1 拍照记录
- **功能描述**：拍摄资产现场照片作为盘点凭证
- **拍照场景**：
  - 资产整体照片
  - 资产标签照片
  - 资产位置环境照片
  - 损坏或异常情况照片
- **技术实现**：
  - 使用 `wx.chooseMedia` API 拍照
  - 自动压缩图片减少存储空间
  - 支持多张照片上传
  - 照片与盘点记录自动关联
- **接口需求**：
  - `POST /common/upload` - 上传照片文件
  - `PUT /asset/stocktaking/record/{recordId}/photos` - 关联照片

#### 4.2 语音备注
- **功能描述**：录制语音备注，方便现场快速记录
- **应用场景**：
  - 复杂差异情况说明
  - 资产状况描述
  - 现场特殊情况记录
- **技术实现**：
  - 使用 `wx.getRecorderManager` API 录音
  - 语音文件自动上传和转换
  - 支持语音转文字功能
- **接口需求**：
  - `POST /common/upload/audio` - 上传语音文件
  - `POST /common/speech-to-text` - 语音转文字

### 5. 进度监控模块

#### 5.1 个人进度查看
- **功能描述**：查看个人盘点任务的完成进度
- **显示内容**：
  - 总任务数和已完成任务数
  - 总资产数和已盘点资产数
  - 完成进度百分比
  - 发现差异的资产数量
  - 预计完成时间
- **图表展示**：
  - 进度条显示完成百分比
  - 饼图显示资产状态分布
  - 柱状图显示每日完成情况
- **接口需求**：
  - `GET /asset/stocktaking/task/my-progress` - 获取个人进度
  - `GET /asset/stocktaking/record/my-statistics` - 获取个人统计

#### 5.2 团队进度查看（组长权限）
- **功能描述**：查看团队整体盘点进度（仅盘点组长可见）
- **显示内容**：
  - 团队成员盘点进度排行
  - 各任务完成情况
  - 差异问题汇总
  - 进度预警提醒
- **管理功能**：
  - 查看成员详细进度
  - 任务重新分配建议
  - 进度异常提醒
- **接口需求**：
  - `GET /asset/stocktaking/plan/{planId}/team-progress` - 获取团队进度
  - `GET /asset/stocktaking/task/team-statistics` - 获取团队统计

### 6. 历史记录模块

#### 6.1 盘点历史查询
- **功能描述**：查看个人历史盘点记录
- **查询维度**：
  - 按时间范围查询（今日、本周、本月、自定义）
  - 按盘点计划查询
  - 按资产类别查询
  - 按差异状态查询
- **显示信息**：
  - 盘点时间和地点
  - 资产基本信息
  - 盘点结果和差异情况
  - 关联的照片和备注
- **接口需求**：
  - `GET /asset/stocktaking/record/my-history` - 获取历史记录
  - `GET /asset/stocktaking/record/{recordId}/details` - 获取记录详情

#### 6.2 资产盘点轨迹
- **功能描述**：查看特定资产的历次盘点记录
- **显示内容**：
  - 资产基本信息
  - 历次盘点时间线
  - 状态变化记录
  - 位置变更记录
  - 差异处理记录
- **接口需求**：
  - `GET /asset/stocktaking/asset/{assetId}/history` - 获取资产盘点历史

### 7. 离线支持模块

#### 7.1 数据缓存
- **功能描述**：缓存必要数据支持离线操作
- **缓存内容**：
  - 当前任务的资产清单
  - 常用差异原因列表
  - 资产分类和位置信息
  - 已完成但未同步的盘点记录
- **技术实现**：
  - 使用 `wx.storage` 本地存储
  - 数据压缩减少存储空间
  - 定期清理过期缓存

#### 7.2 离线盘点
- **功能描述**：在网络不稳定时支持离线盘点操作
- **离线功能**：
  - 扫码识别资产（基于缓存数据）
  - 录入盘点结果
  - 拍照记录（本地存储）
  - 差异标记
- **同步机制**：
  - 网络恢复时自动同步
  - 数据冲突检测和处理
  - 同步进度提示

#### 7.3 数据同步
- **功能描述**：在线时自动同步离线数据
- **同步策略**：
  - 增量同步减少数据传输
  - 优先同步重要数据
  - 失败重试机制
  - 同步状态实时反馈
- **冲突处理**：
  - 检测数据冲突
  - 提供冲突解决选项
  - 保留冲突记录备查

## 技术架构设计

### 1. 技术栈选择

#### 1.1 前端技术
- **开发框架**：微信小程序原生开发
- **UI组件库**：WeUI / Vant Weapp
- **状态管理**：小程序原生数据绑定 + 全局状态管理
- **网络请求**：封装的 wx.request
- **本地存储**：wx.storage / wx.storageSync

#### 1.2 核心API
- **扫码功能**：wx.scanCode
- **拍照功能**：wx.chooseMedia
- **录音功能**：wx.getRecorderManager
- **文件上传**：wx.uploadFile
- **位置服务**：wx.getLocation
- **网络状态**：wx.getNetworkType

### 2. 数据存储策略

#### 2.1 本地存储结构
```javascript
// 本地存储键值定义
const StorageKeys = {
  USER_INFO: 'asset_stocktaking_user_info',
  CURRENT_TASK: 'asset_stocktaking_current_task',
  OFFLINE_RECORDS: 'asset_stocktaking_offline_records',
  CACHED_ASSETS: 'asset_stocktaking_cached_assets',
  COMMON_REASONS: 'asset_stocktaking_common_reasons',
  APP_CONFIG: 'asset_stocktaking_app_config'
};
```

#### 2.2 数据缓存策略
- **任务数据**：缓存当前任务的完整资产清单
- **用户数据**：缓存用户基本信息和权限
- **字典数据**：缓存差异原因、资产状态等字典
- **离线数据**：缓存未同步的盘点记录

### 3. 接口设计规范

#### 3.1 请求封装
```javascript
// API请求封装
class ApiService {
  static baseURL = 'https://your-domain.com/api';

  static request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.baseURL + options.url,
        method: options.method || 'GET',
        data: options.data,
        header: {
          'Content-Type': 'application/json',
          'Authorization': wx.getStorageSync('token')
        },
        success: resolve,
        fail: reject
      });
    });
  }
}
```

#### 3.2 接口列表
```javascript
// 微信小程序专用接口
const MiniProgramAPI = {
  // 认证相关
  wechatLogin: 'POST /auth/wechat/login',
  getUserInfo: 'GET /auth/user/info',

  // 任务管理
  getMyTasks: 'POST /asset/stocktaking/task/list', // 使用 onlyMyTasks=true
  getTaskDetail: 'GET /asset/stocktaking/task/{taskId}',
  startTask: 'PUT /asset/stocktaking/task/start/{taskId}',

  // 盘点执行
  scanInventory: 'POST /asset/stocktaking/record/scan',
  getAssetDetail: 'GET /asset/detail',
  getRecordByCode: 'GET /asset/stocktaking/record/code/{assetCode}',
  createRecord: 'POST /asset/stocktaking/record',
  updateRecord: 'PUT /asset/stocktaking/record/{recordId}',

  // 差异处理
  createDifference: 'POST /asset/stocktaking/difference',
  updateDifference: 'PUT /asset/stocktaking/difference/{diffId}',

  // 文件上传
  uploadPhoto: 'POST /common/upload/photo',
  uploadAudio: 'POST /common/upload/audio',

  // 进度查询
  getTaskProgress: 'GET /asset/stocktaking/record/progress/{taskId}',
  getMyTasks: 'POST /asset/stocktaking/task/list', // 使用 onlyMyTasks=true

  // 历史记录
  getMyRecords: 'POST /asset/stocktaking/record/list', // 使用 onlyMyRecords=true
  getAssetHistory: 'GET /asset/stocktaking/record/asset/{assetId}'
};
```

## 用户界面设计

### 1. 页面结构设计

```
pages/
├── index/                    # 首页
│   └── index.js/wxml/wxss
├── auth/                     # 认证相关
│   ├── login/               # 登录页面
│   └── bind/                # 账号绑定
├── task/                     # 任务管理
│   ├── list/                # 任务列表
│   ├── detail/              # 任务详情
│   └── progress/            # 进度查看
├── stocktaking/             # 盘点执行
│   ├── scan/                # 扫码盘点
│   ├── record/              # 记录详情
│   └── difference/          # 差异处理
├── history/                 # 历史记录
│   ├── list/                # 记录列表
│   └── detail/              # 记录详情
├── profile/                 # 个人中心
│   └── index/               # 个人信息
└── components/              # 公共组件
    ├── asset-card/          # 资产卡片
    ├── progress-bar/        # 进度条
    ├── photo-uploader/      # 照片上传
    └── scanner/             # 扫码组件
```

### 2. 关键页面设计

#### 2.1 首页设计
```
┌─────────────────────────────┐
│  资产盘点                   │
├─────────────────────────────┤
│ 👤 张三，欢迎使用           │
├─────────────────────────────┤
│ 📊 今日进度                 │
│    已完成：15/30 (50%)      │
│    ████████░░░░░░░░ 50%     │
├─────────────────────────────┤
│ 🎯 快速操作                 │
│ [📷 扫码盘点] [📋 我的任务]  │
│ [📈 查看进度] [📚 历史记录]  │
├─────────────────────────────┤
│ 📢 通知公告                 │
│ • 第一季度盘点即将开始      │
│ • 请及时完成分配的任务      │
└─────────────────────────────┘
```

#### 2.2 扫码盘点页面
```
┌─────────────────────────────┐
│  扫码盘点                   │
├─────────────────────────────┤
│ [📷 扫描资产标签]           │
│ [⌨️ 手动输入编码]           │
├─────────────────────────────┤
│ 📦 资产信息                 │
│ 名称：办公桌                │
│ 编码：ZC001                 │
│ 分类：办公家具              │
│ 账面位置：办公室101         │
│ 账面状态：正常使用          │
├─────────────────────────────┤
│ ✅ 盘点确认                 │
│ 实际位置：[办公室101▼]      │
│ 实际状态：[正常使用▼]       │
│ 发现状态：☑️找到 ☐未找到    │
├─────────────────────────────┤
│ [📷 拍照记录] [🎤 语音备注]  │
│ [💾 保存结果] [⏭️ 下一个]    │
└─────────────────────────────┘
```

#### 2.3 任务列表页面
```
┌─────────────────────────────┐
│  我的盘点任务               │
├─────────────────────────────┤
│ 🔍 [搜索任务]               │
│ 📊 [全部▼] [进行中▼]        │
├─────────────────────────────┤
│ 📋 办公区域盘点任务         │
│    计划：2025年第一季度盘点  │
│    状态：进行中 🟡          │
│    进度：25/50 (50%)        │
│    截止：2025-01-30         │
│    [查看详情] [继续盘点]    │
├─────────────────────────────┤
│ 📋 仓库A区盘点任务          │
│    计划：2025年第一季度盘点  │
│    状态：待开始 ⚪          │
│    进度：0/80 (0%)          │
│    截止：2025-02-05         │
│    [查看详情] [开始盘点]    │
└─────────────────────────────┘
```

### 3. 交互设计原则

#### 3.1 操作便捷性
- **大按钮设计**：适合现场操作的大尺寸按钮
- **单手操作**：主要功能支持单手操作
- **快速录入**：减少输入步骤，多用选择操作
- **语音辅助**：支持语音录入和语音反馈

#### 3.2 信息清晰性
- **状态明确**：用颜色和图标明确表示状态
- **进度可视**：用进度条直观显示完成情况
- **层次分明**：重要信息突出显示
- **反馈及时**：操作结果立即反馈

#### 3.3 容错性设计
- **操作确认**：重要操作需要二次确认
- **撤销功能**：支持撤销最近的操作
- **数据保护**：自动保存防止数据丢失
- **异常处理**：友好的错误提示和处理建议

## 开发实施计划

### 第一阶段：基础功能开发（2周）
- **用户认证模块**：微信登录、账号绑定
- **任务管理模块**：任务列表、任务详情
- **基础盘点功能**：扫码识别、信息确认
- **数据存储**：本地缓存、基础同步

### 第二阶段：核心功能完善（2周）
- **盘点执行模块**：完整的盘点流程
- **差异处理模块**：差异识别、处理流程
- **拍照记录功能**：照片上传、关联
- **离线支持**：离线盘点、数据同步

### 第三阶段：增强功能开发（1.5周）
- **进度监控模块**：个人进度、团队进度
- **历史记录模块**：记录查询、轨迹追踪
- **语音功能**：语音备注、语音转文字
- **性能优化**：加载优化、体验优化

### 第四阶段：测试与发布（0.5周）
- **功能测试**：完整功能测试
- **兼容性测试**：不同设备测试
- **性能测试**：网络环境测试
- **用户体验测试**：现场使用测试

## 质量保证

### 1. 功能测试要求
- **扫码准确性**：二维码/条形码识别准确率 > 95%
- **数据同步**：离线数据同步成功率 > 99%
- **响应速度**：页面加载时间 < 2秒
- **稳定性**：连续使用4小时无崩溃

### 2. 用户体验要求
- **操作流畅性**：操作响应时间 < 500ms
- **界面友好性**：符合微信小程序设计规范
- **学习成本**：新用户5分钟内掌握基本操作
- **错误处理**：异常情况有明确提示和解决方案

### 3. 安全性要求
- **数据加密**：敏感数据传输加密
- **权限控制**：严格的用户权限验证
- **数据保护**：本地数据安全存储
- **隐私保护**：符合微信小程序隐私规范

## 总结

本微信小程序资产盘点功能设计基于已完成的后端系统，专注于移动端盘点作业的核心需求。通过充分利用移动设备的扫码、拍照、语音等特性，为盘点人员提供便捷高效的作业工具。设计遵循简洁、高效、可靠的原则，确保在各种现场环境下都能稳定运行，显著提升资产盘点的效率和准确性。
