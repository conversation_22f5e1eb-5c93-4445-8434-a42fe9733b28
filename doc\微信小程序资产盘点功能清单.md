# 微信小程序资产盘点功能清单

## 功能对比分析

基于已完成的后端接口和网页端功能，以下是微信小程序端需要实现的功能清单：

### 网页端 vs 小程序端功能对比

| 功能模块 | 网页端功能 | 小程序端功能 | 实现优先级 |
|---------|-----------|-------------|-----------|
| **用户管理** | 完整的用户管理、权限配置 | 微信登录、账号绑定 | 高 |
| **盘点计划** | 创建、编辑、审批盘点计划 | 查看分配的计划（只读） | 中 |
| **盘点任务** | 创建、分配、管理任务 | 查看我的任务、执行任务 | 高 |
| **资产盘点** | 基础盘点功能 | 扫码盘点、拍照记录 | 高 |
| **差异处理** | 差异分析、批量处理 | 现场差异标记、简单处理 | 高 |
| **进度监控** | 全局进度监控、报表 | 个人进度、团队进度 | 中 |
| **报告生成** | 复杂报表、数据导出 | 简单统计查看 | 低 |
| **历史记录** | 完整历史数据管理 | 个人历史记录查询 | 中 |

## 核心功能模块详细清单

### 2. 盘点任务管理模块 ⭐⭐⭐

#### 2.1 我的任务列表
- [ ] 获取分配给我的任务
- [ ] 任务状态筛选
- [ ] 任务搜索功能
- [ ] 任务排序（按截止时间、进度等）
- [ ] 任务详情查看

#### 2.2 任务执行管理
- [ ] 开始执行任务
- [ ] 暂停/继续任务
- [ ] 完成任务确认
- [ ] 任务进度实时更新

#### 2.3 任务资产清单
- [ ] 查看任务包含的资产列表
- [ ] 资产状态筛选（待盘点、已完成、有差异）
- [ ] 资产搜索功能
- [ ] 资产详情查看

**接口依赖**：
- `POST /asset/stocktaking/task/list` (使用 onlyMyTasks=true 参数)
- `GET /asset/stocktaking/task/{taskId}`
- `PUT /asset/stocktaking/task/start/{taskId}`
- `POST /asset/stocktaking/record/list` (查询任务相关资产)

### 3. 资产盘点执行模块 ⭐⭐⭐

#### 3.1 扫码盘点功能
- [ ] 二维码扫描识别
- [ ] 条形码扫描识别
- [ ] 手动输入资产编码
- [ ] 扫码结果验证
- [ ] 扫码历史记录

#### 3.2 资产信息确认
- [ ] 显示资产基本信息
- [ ] 账面信息 vs 实际信息对比
- [ ] 资产状态选择
- [ ] 位置信息确认
- [ ] 发现状态确认（找到/未找到）

#### 3.3 盘点结果录入
- [ ] 实际状态录入
- [ ] 实际位置录入
- [ ] 盘点时间自动记录
- [ ] 盘点备注录入
- [ ] 结果保存和提交

**接口依赖**：
- `POST /asset/stocktaking/record/scan` - 扫码盘点（包含资产信息获取）
- `GET /asset/stocktaking/record/code/{assetCode}` - 根据资产编码查询记录
- `GET /asset/detail` - 根据资产ID获取详细信息
- `POST /asset/stocktaking/record` - 创建盘点记录
- `PUT /asset/stocktaking/record/{recordId}` - 更新盘点记录

### 4. 差异处理模块 ⭐⭐⭐

#### 4.1 差异识别
- [ ] 自动识别盘盈资产
- [ ] 自动识别盘亏资产
- [ ] 自动识别状态差异
- [ ] 自动识别位置差异
- [ ] 差异程度评估

#### 4.2 差异处理
- [ ] 差异类型选择
- [ ] 差异原因录入
- [ ] 差异说明填写
- [ ] 处理建议录入
- [ ] 差异照片上传

#### 4.3 差异报告
- [ ] 差异记录创建
- [ ] 差异状态更新
- [ ] 差异处理跟踪
- [ ] 差异统计查看

**接口依赖**：
- `POST /asset/stocktaking/difference`
- `PUT /asset/stocktaking/difference/{diffId}`
- `GET /asset/stocktaking/difference/list`

### 5. 现场记录模块 ⭐⭐

#### 5.1 拍照记录功能
- [ ] 资产现场拍照
- [ ] 多张照片上传
- [ ] 照片自动压缩
- [ ] 照片与记录关联
- [ ] 照片预览和删除

#### 5.2 语音备注功能
- [ ] 语音录制功能
- [ ] 语音文件上传
- [ ] 语音转文字
- [ ] 语音播放功能
- [ ] 语音备注管理

#### 5.3 位置记录功能
- [ ] 自动获取GPS位置
- [ ] 位置信息记录
- [ ] 位置与盘点记录关联
- [ ] 位置轨迹记录

**接口依赖**：
- `POST /common/upload/photo`
- `POST /common/upload/audio`
- `POST /common/speech-to-text`

### 6. 进度监控模块 ⭐⭐

#### 6.1 个人进度查看
- [ ] 个人任务完成进度
- [ ] 个人盘点统计
- [ ] 进度图表展示
- [ ] 完成情况分析
- [ ] 效率统计分析

#### 6.2 团队进度查看（组长权限）
- [ ] 团队整体进度
- [ ] 成员进度排行
- [ ] 任务分配情况
- [ ] 进度异常提醒
- [ ] 团队效率分析

**接口依赖**：
- `GET /asset/stocktaking/record/progress/{taskId}` - 获取任务盘点进度
- `POST /asset/stocktaking/task/list` (使用 onlyMyTasks=true) - 获取我的任务进度
- `POST /asset/stocktaking/record/list` (使用 onlyMyRecords=true) - 获取我的盘点统计

### 7. 历史记录模块 ⭐

#### 7.1 盘点历史查询
- [ ] 个人盘点历史
- [ ] 按时间范围查询
- [ ] 按盘点计划查询
- [ ] 按差异状态查询
- [ ] 历史记录详情

#### 7.2 资产盘点轨迹
- [ ] 资产历次盘点记录
- [ ] 资产状态变化轨迹
- [ ] 资产位置变更记录
- [ ] 差异处理历史

**接口依赖**：
- `POST /asset/stocktaking/record/list` (使用 onlyMyRecords=true) - 获取个人历史记录
- `GET /asset/stocktaking/record/asset/{assetId}` - 获取资产盘点历史

### 8. 离线支持模块 ⭐⭐

#### 8.1 数据缓存
- [ ] 任务数据本地缓存
- [ ] 资产信息缓存
- [ ] 字典数据缓存
- [ ] 用户信息缓存
- [ ] 缓存数据管理

#### 8.2 离线盘点
- [ ] 离线扫码识别
- [ ] 离线数据录入
- [ ] 离线照片存储
- [ ] 离线状态提示
- [ ] 数据完整性保证

#### 8.3 数据同步
- [ ] 自动同步机制
- [ ] 手动同步功能
- [ ] 同步状态显示
- [ ] 冲突检测处理
- [ ] 同步失败重试

## 技术实现要点

### 1. 微信小程序特有功能
- [ ] `wx.scanCode` - 扫码功能
- [ ] `wx.chooseMedia` - 拍照功能
- [ ] `wx.getRecorderManager` - 录音功能
- [ ] `wx.uploadFile` - 文件上传
- [ ] `wx.getLocation` - 位置获取
- [ ] `wx.getNetworkType` - 网络状态

### 2. 数据存储管理
- [ ] `wx.storage` - 本地数据存储
- [ ] 数据加密存储
- [ ] 缓存策略管理
- [ ] 数据清理机制
- [ ] 存储空间优化

### 3. 网络请求封装
- [ ] 统一请求封装
- [ ] 请求拦截器
- [ ] 响应拦截器
- [ ] 错误处理机制
- [ ] 重试机制

### 4. 用户体验优化
- [ ] 加载状态提示
- [ ] 操作反馈提示
- [ ] 错误友好提示
- [ ] 网络状态提示
- [ ] 性能优化

## 开发优先级建议

### 第一期（核心功能）- 高优先级 ⭐⭐⭐
1. 用户认证与权限模块
2. 盘点任务管理模块
3. 资产盘点执行模块
4. 差异处理模块

### 第二期（增强功能）- 中优先级 ⭐⭐
1. 现场记录模块
2. 进度监控模块
3. 离线支持模块

### 第三期（扩展功能）- 低优先级 ⭐
1. 历史记录模块
2. 高级统计功能
3. 性能优化
4. 用户体验优化

## 预期效果

通过微信小程序端的实现，预期达到以下效果：

1. **提升盘点效率**：扫码功能提升资产识别速度
2. **提高数据准确性**：现场拍照和语音备注减少错误
3. **增强用户体验**：移动端操作更加便捷
4. **支持离线作业**：网络不稳定环境下仍可正常工作
5. **实时数据同步**：盘点结果实时同步到后端系统
